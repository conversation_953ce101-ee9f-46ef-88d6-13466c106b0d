/* consignee.css */
body {
    font-family: 'Inter', sans-serif;
    background-color: #0a0a0a; 
    color: #e5e5e5; 
    /* <PERSON>l<PERSON>'s min-h-screen and p-4/sm:p-6/md:p-8 are applied directly in HTML */
}

/* Custom scrollbar */
::-webkit-scrollbar { 
    width: 8px; 
    height: 8px; 
}
::-webkit-scrollbar-track { 
    background: #1f1f1f; 
}
::-webkit-scrollbar-thumb { 
    background: #525252; 
    border-radius: 4px; 
}
::-webkit-scrollbar-thumb:hover { 
    background: #737373; 
}

/* Modal animations */
.modal-enter { 
    opacity: 0; 
    transform: scale(0.95) translateY(-20px); 
}
.modal-enter-active { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
    transition: opacity 300ms ease-out, transform 300ms ease-out; 
}
.modal-leave { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
}
.modal-leave-active { 
    opacity: 0; 
    transform: scale(0.95) translateY(-20px); 
    transition: opacity 200ms ease-in, transform 200ms ease-in; 
}

/* Table row selection */
.row-selected { 
    background-color: #404040 !important; /* !important to override Tailwind hover if necessary */
}
.row-selected td { 
    color: #fafafa; 
}

/* Custom checkbox (Tailwind utility classes are powerful, but if specific overrides are needed) */
.custom-checkbox {
    appearance: none; 
    -webkit-appearance: none; 
    height: 1.25rem; /* h-5 */
    width: 1.25rem; /* w-5 */
    background-color: #262626; /* bg-neutral-800, slightly adjusted for contrast */
    border: 1px solid #525252; /* border-neutral-600 */
    border-radius: 0.25rem; /* rounded */
    display: inline-block; 
    position: relative; 
    cursor: pointer;
    transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}
.custom-checkbox:checked { 
    background-color: #a3a3a3; /* bg-neutral-400 */
    border-color: #d4d4d4; /* border-neutral-300 */
}
.custom-checkbox:checked::after {
    content: ''; 
    position: absolute; 
    left: 0.375rem; /* Adjust for checkmark position */
    top: 0.125rem;  /* Adjust for checkmark position */
    width: 0.375rem; /* w-1.5 */
    height: 0.75rem; /* h-3 */
    border: solid #171717; /* Dark checkmark for contrast on light gray */
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Sticky table header */
.table-header-sticky th {
    position: sticky; 
    top: 0; 
    z-index: 10; /* Ensure header is above table content */
    background-color: #171717; /* bg-neutral-900, very dark */
    border-bottom-width: 1px;
    border-color: #404040; /* border-neutral-700 */
}

/* Spinner for loading states */
.spinner {
    border: 2px solid rgba(229, 229, 229, 0.3); /* Light gray spinner base */
    border-radius: 50%;
    border-top-color: #e5e5e5; /* Light gray spinner active part */
    width: 1rem; /* w-4 */
    height: 1rem; /* h-4 */
    animation: spin 1s linear infinite; 
    display: inline-block; 
    margin-right: 0.5rem; /* mr-2 */
}
@keyframes spin { 
    to { transform: rotate(360deg); } 
}

/* Sortable header styling */
.sortable-header { 
    cursor: pointer; 
    user-select: none;
}
.sortable-header:hover { 
    color: #cccccc; /* Lighter gray for hover */
} 
.sort-indicator { 
    margin-left: 0.5rem; /* ml-2 */
    font-size: 0.75rem; /* text-xs */
}

/* Toast Notification Styling */
#toast-container {
    position: fixed; 
    top: 1.5rem; /* Adjust as needed */
    right: 1.5rem; /* Adjust as needed */
    z-index: 100; /* High z-index */
    display: flex; 
    flex-direction: column; 
    gap: 0.75rem; /* gap-3 */
}
.toast {
    display: flex; 
    align-items: center; 
    padding: 1rem; /* p-4 */
    border-radius: 0.5rem; /* rounded-lg */
    box-shadow: 0 10px 15px -3px rgba(255,255,255,0.05), 0 4px 6px -2px rgba(255,255,255,0.03); /* Subtle white shadow for dark theme */
    min-width: 250px; 
    max-width: 350px; 
    opacity: 0;
    transform: translateX(100%); 
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    background-color: #262626; /* bg-neutral-800 */
    color: #e5e5e5; /* text-neutral-200 */
    border: 1px solid #525252; /* border-neutral-600 */
}
.toast.show { 
    opacity: 1; 
    transform: translateX(0); 
}
.toast-icon { 
    margin-right: 0.75rem; /* mr-3 */
    font-size: 1.25rem; /* text-xl */
}
/* Icon colors for different toast types */
.toast-success .toast-icon { color: #86efac; /* Tailwind green-300 */ } 
.toast-error .toast-icon { color: #fca5a5; /* Tailwind red-300 */ } 
.toast-info .toast-icon { color: #93c5fd; /* Tailwind blue-300 */ } 
.toast-warning .toast-icon { color: #fde047; /* Tailwind yellow-300 */ } 

/* Default consignee row highlight */
.default-consignee-row td:first-child { 
    border-left: 3px solid #a3a3a3; /* border-neutral-400 */
}
.default-consignee-row { 
    background-color: rgba(64, 64, 64, 0.3); /* Darker tint for default */
}
.default-consignee-row .fa-star { 
    color: #e5e5e5; /* text-neutral-200 */
}

/* Custom Searchable Dropdown for Branch */
.searchable-dropdown-container { 
    position: relative; 
    /* Tailwind w-full sm:w-64 applied in HTML */
}
.searchable-dropdown-list {
    position: absolute; 
    top: 100%; 
    left: 0; 
    right: 0;
    background-color: #1c1c1c; /* Slightly lighter than body for dropdown */
    border: 1px solid #525252; /* border-neutral-600 */
    border-top: none; 
    border-radius: 0 0 0.5rem 0.5rem; /* rounded-b-lg */
    max-height: 200px; 
    overflow-y: auto; 
    z-index: 50; /* Ensure it's above other content but below modals */
    /* Tailwind 'hidden' class toggled by JS */
}
.searchable-dropdown-item {
    padding: 0.75rem 1rem; /* p-3 px-4 */
    cursor: pointer; 
    color: #d4d4d4; /* text-neutral-300 */
}
.searchable-dropdown-item:hover, 
.searchable-dropdown-item.selected { /* 'selected' class added by JS */
    background-color: #404040; /* bg-neutral-700 */
    color: #fafafa; /* text-neutral-50 */
}
.searchable-dropdown-input { /* Tailwind classes applied in HTML */
    border-radius: 0.5rem; /* rounded-lg */
}
.searchable-dropdown-container.open .searchable-dropdown-input { /* 'open' class added by JS */
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* Custom select for consignee type in modal */
.custom-select-wrapper { 
    position: relative; 
}
.custom-select-wrapper select { /* Tailwind classes for base styling are in HTML */
    appearance: none; 
    -webkit-appearance: none;
    /* SVG for dropdown arrow (Tailwind doesn't have a direct utility for this specific SVG background) */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center; /* pr-10 equivalent for arrow positioning */
    background-size: 1.25em 1.25em; /* Adjust size as needed */
    padding-right: 2.5rem; /* Make space for arrow, Tailwind pr-10 */
}
